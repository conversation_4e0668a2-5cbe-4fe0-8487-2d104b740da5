package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分派操作结果DTO
 *
 * 用于返回分派操作的执行结果
 * 包含总数、成功数量、失败数量、批次号等信息
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("分派操作结果DTO")
public class DispatchResultDTO {

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer total;

    /**
     * 成功数量
     */
    @ApiModelProperty(value = "成功数量")
    private Integer succCnt;

    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量")
    private Integer failCnt;

    /**
     * 批次号（用于导出异常数据）
     */
    @ApiModelProperty(value = "批次号，用于导出异常数据")
    private String batchNo;

    /**
     * 操作结果摘要
     */
    @ApiModelProperty(value = "操作结果摘要")
    private String summary;

    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        if (total == null || total == 0) {
            return 0.0;
        }
        return (double) succCnt / total * 100;
    }

    /**
     * 获取操作结果摘要
     */
    public String getSummary() {
        if (summary != null) {
            return summary;
        }

        StringBuilder sb = new StringBuilder();
        sb.append("分派完成，");
        sb.append("总计").append(total).append("条，");
        sb.append("成功").append(succCnt).append("条，");
        sb.append("失败").append(failCnt).append("条");

        if (failCnt > 0) {
            sb.append("，可通过批次号[").append(batchNo).append("]导出异常数据");
        }

        return sb.toString();
    }

    /**
     * 判断是否全部成功
     */
    public boolean isAllSuccess() {
        return failCnt == null || failCnt == 0;
    }

    /**
     * 判断是否全部失败
     */
    public boolean isAllFailed() {
        return succCnt == null || succCnt == 0;
    }
}
