package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分派异常数据DTO
 *
 * 用于记录分派操作过程中失败的交付单信息
 * 简化版本，只包含必要的字段用于Excel导出
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("分派异常数据DTO")
public class DispatchErrorDTO {

    /**
     * 交付单编号
     */
    @Excel(name = "交付单编号")
    @ApiModelProperty(value = "交付单编号")
    private String deliveryOrderNo;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息")
    @ApiModelProperty(value = "错误信息")
    private String errorInfo;

    /**
     * 创建错误数据的静态方法
     */
    public static DispatchErrorDTO createError(String deliveryOrderNo, String errorInfo) {
        return DispatchErrorDTO.builder()
                .deliveryOrderNo(deliveryOrderNo)
                .errorInfo(errorInfo)
                .build();
    }
}
