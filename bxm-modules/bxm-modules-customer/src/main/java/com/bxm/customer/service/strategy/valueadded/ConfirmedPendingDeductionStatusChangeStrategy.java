package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 已确认待扣款状态变更策略
 *
 * 处理从"已确认待扣款"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. CONFIRMED_PENDING_DEDUCTION -> DEDUCTION_COMPLETED (扣款正常完成)
 * 2. CONFIRMED_PENDING_DEDUCTION -> DEDUCTION_EXCEPTION (扣款异常)
 * 3. CONFIRMED_PENDING_DEDUCTION -> PENDING_CONFIRMATION (退回待确认)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class ConfirmedPendingDeductionStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED ||
               targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED) {
            validateCustomerInfo(order);
            validateCreditCode(order);
            validateTaxpayerType(order);
            if (order.getSyncHandlingFee() == null) {
                throw new IllegalArgumentException("是否同步手续费标志不能为空");
            }
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            validateReturnOperation(request, "待确认状态");
        } else if (targetStatus != ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION) {
            throwUnsupportedTransition("已确认待扣款", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION;
    }
}
