package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.ValueAddedAccountPeriodService;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 待提交状态变更策略
 *
 * 处理从"已保存待提交"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. SAVED_PENDING_SUBMIT -> SUBMITTED_PENDING_DELIVERY (正常提交)
 * 2. SAVED_PENDING_SUBMIT -> DRAFT (退回草稿)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class PendingSubmitStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Autowired
    private ValueAddedAccountPeriodService valueAddedAccountPeriodService;

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY ||
               targetStatus == ValueAddedDeliveryOrderStatus.DRAFT;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            validateNotEmpty(order.getCustomerName(), "客户企业名称");
            validateCreditCode(order);
            if (order.getValueAddedItemTypeId() == null) {
                throw new IllegalArgumentException("增值事项类型不能为空");
            }

            try {
                valueAddedAccountPeriodService.validateAccountPeriodForSubmit(order);
                valueAddedAccountPeriodService.generateValueAddedPeriodRecords(order);
            } catch (IllegalArgumentException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException("账期处理失败: " + e.getMessage(), e);
            }
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DRAFT) {
            validateReturnOperation(request, "草稿状态");
        } else {
            throwUnsupportedTransition("待提交", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT;
    }
}
