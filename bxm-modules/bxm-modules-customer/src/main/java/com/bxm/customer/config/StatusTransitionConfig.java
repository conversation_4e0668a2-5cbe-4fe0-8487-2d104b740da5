package com.bxm.customer.config;

import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 状态转换配置类
 * 
 * 定义增值交付单状态转换的配置规则
 * 可通过配置文件进行状态转换规则的调整
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bxm.delivery-order.status-transition")
public class StatusTransitionConfig {

    /**
     * 状态转换规则映射
     * key: 当前状态代码
     * value: 可转换的目标状态代码列表
     */
    private Map<String, List<String>> rules = new HashMap<>();

    /**
     * 是否启用严格模式
     * 严格模式下，只允许配置文件中定义的状态转换
     * 非严格模式下，会结合代码中的策略进行验证
     */
    private boolean strictMode = false;

    /**
     * 状态转换日志级别
     * DEBUG: 记录详细的转换过程
     * INFO: 记录关键转换信息
     * WARN: 仅记录异常转换
     */
    private String logLevel = "INFO";

    /**
     * 是否启用状态转换审计
     * 启用后会记录所有状态转换的详细信息
     */
    private boolean auditEnabled = true;

    /**
     * 状态转换超时时间（秒）
     * 超过此时间的状态转换操作将被中断
     */
    private int timeoutSeconds = 30;

    /**
     * 获取指定状态的可转换目标状态列表
     * 
     * @param currentStatusCode 当前状态代码
     * @return 可转换的目标状态代码列表
     */
    public List<String> getAllowedTargetStatuses(String currentStatusCode) {
        return rules.getOrDefault(currentStatusCode, Arrays.asList());
    }

    /**
     * 检查状态转换是否被配置允许
     * 
     * @param currentStatusCode 当前状态代码
     * @param targetStatusCode 目标状态代码
     * @return 是否允许转换
     */
    public boolean isTransitionAllowed(String currentStatusCode, String targetStatusCode) {
        List<String> allowedTargets = getAllowedTargetStatuses(currentStatusCode);
        return allowedTargets.contains(targetStatusCode);
    }

    /**
     * 初始化默认的状态转换规则
     * 当配置文件中没有配置时使用此默认规则
     */
    @PostConstruct
    public void initDefaultRules() {
        if (rules.isEmpty()) {
            // 草稿状态 -> 待提交 或 直接提交到待交付
            rules.put(ValueAddedDeliveryOrderStatus.DRAFT.getCode(),
                    Arrays.asList(
                        ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT.getCode(),
                        ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode()
                    ));

            // 待提交状态 -> 已提交待交付 或 退回草稿
            rules.put(ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT.getCode(),
                    Arrays.asList(
                        ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode(),
                        ValueAddedDeliveryOrderStatus.DRAFT.getCode()
                    ));

            // 已提交待交付 -> 已交付待确认 或 交付异常 或 退回待提交
            rules.put(ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode(),
                    Arrays.asList(
                        ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION.getCode(),
                        ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION.getCode(),
                        ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT.getCode()
                    ));

            // 已交付待确认 -> 已确认待扣款 或 退回重新交付
            rules.put(ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION.getCode(),
                    Arrays.asList(
                        ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode(),
                        ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode()
                    ));

            // 已确认待扣款 -> 已扣款 或 扣款异常 或 退回待确认
            rules.put(ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode(),
                    Arrays.asList(
                        ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode(),
                        ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION.getCode(),
                        ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION.getCode()
                    ));

            // 已扣款（终态）-> 扣款异常（特殊情况）
            rules.put(ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode(),
                    Arrays.asList(ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION.getCode()));

            // 扣款异常 -> 已关闭扣款 或 重新扣款 或 异常解决
            rules.put(ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION.getCode(),
                    Arrays.asList(
                        ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED.getCode(),
                        ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode(),
                        ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode()
                    ));

            // 交付异常 -> 已关闭交付 或 重新交付 或 异常解决
            rules.put(ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION.getCode(),
                    Arrays.asList(
                        ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED.getCode(),
                        ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode(),
                        ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION.getCode()
                    ));

            // 终态状态（已关闭扣款、已关闭交付）不允许转换
            rules.put(ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED.getCode(), Arrays.asList());
            rules.put(ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED.getCode(), Arrays.asList());
        }
    }
}
