package com.bxm.customer.utils;

import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.lang.Nullable;

import java.beans.PropertyDescriptor;
import java.util.HashSet;
import java.util.Set;

/**
 * Bean工具类，扩展Spring BeanUtils功能
 * 
 * <AUTHOR>
 */
public class BeanUtils extends org.springframework.beans.BeanUtils {

    /**
     * 复制属性，忽略源对象中的null值
     * 
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyIgnoreNull(Object source, Object target) {
        copyIgnoreNull(source, target, (String[]) null);
    }

    /**
     * 复制属性，忽略源对象中的null值和指定的属性
     * 
     * @param source 源对象
     * @param target 目标对象
     * @param ignoreProperties 要忽略的属性名数组
     */
    public static void copyIgnoreNull(Object source, Object target, @Nullable String... ignoreProperties) {
        // 获取源对象中所有null值的属性名
        String[] nullPropertyNames = getNullPropertyNames(source);
        
        // 合并null属性和用户指定的忽略属性
        Set<String> ignorePropertiesSet = new HashSet<>();
        if (nullPropertyNames != null) {
            for (String nullProperty : nullPropertyNames) {
                ignorePropertiesSet.add(nullProperty);
            }
        }
        if (ignoreProperties != null) {
            for (String ignoreProperty : ignoreProperties) {
                ignorePropertiesSet.add(ignoreProperty);
            }
        }
        
        // 使用Spring BeanUtils进行属性复制
        copyProperties(source, target, ignorePropertiesSet.toArray(new String[0]));
    }

    /**
     * 复制属性，忽略源对象中的null值和空字符串
     * 
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyIgnoreNullAndEmpty(Object source, Object target) {
        copyIgnoreNullAndEmpty(source, target, (String[]) null);
    }

    /**
     * 复制属性，忽略源对象中的null值、空字符串和指定的属性
     * 
     * @param source 源对象
     * @param target 目标对象
     * @param ignoreProperties 要忽略的属性名数组
     */
    public static void copyIgnoreNullAndEmpty(Object source, Object target, @Nullable String... ignoreProperties) {
        // 获取源对象中所有null值和空字符串的属性名
        String[] nullAndEmptyPropertyNames = getNullAndEmptyPropertyNames(source);
        
        // 合并null/empty属性和用户指定的忽略属性
        Set<String> ignorePropertiesSet = new HashSet<>();
        if (nullAndEmptyPropertyNames != null) {
            for (String nullProperty : nullAndEmptyPropertyNames) {
                ignorePropertiesSet.add(nullProperty);
            }
        }
        if (ignoreProperties != null) {
            for (String ignoreProperty : ignoreProperties) {
                ignorePropertiesSet.add(ignoreProperty);
            }
        }
        
        // 使用Spring BeanUtils进行属性复制
        copyProperties(source, target, ignorePropertiesSet.toArray(new String[0]));
    }

    /**
     * 获取对象中所有null值的属性名
     * 
     * @param source 源对象
     * @return null值属性名数组
     */
    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }

        return emptyNames.toArray(new String[0]);
    }

    /**
     * 获取对象中所有null值和空字符串的属性名
     * 
     * @param source 源对象
     * @return null值和空字符串属性名数组
     */
    private static String[] getNullAndEmptyPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            } else if (srcValue instanceof String && ((String) srcValue).trim().isEmpty()) {
                emptyNames.add(pd.getName());
            }
        }

        return emptyNames.toArray(new String[0]);
    }
}