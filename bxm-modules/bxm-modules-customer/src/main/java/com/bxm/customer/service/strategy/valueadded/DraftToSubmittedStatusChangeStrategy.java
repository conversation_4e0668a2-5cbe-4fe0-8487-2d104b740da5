package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 草稿直接提交到待交付状态变更策略
 *
 * 处理从"草稿"状态直接提交到"待交付"状态的转换
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class DraftToSubmittedStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedTargetStatus() {
        return ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedSourceStatuses() {
        return Arrays.asList(ValueAddedDeliveryOrderStatus.DRAFT);
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        // 直接提交到待交付状态需要更严格的验证
        validateNotEmpty(order.getCustomerName(), "客户名称");
        validateCreditCode(order);
        if (order.getBusinessTopDeptId() == null) {
            throw new IllegalArgumentException("顶级业务部门不能为空");
        }
        validateCustomerInfo(order);
        validateTaxpayerType(order);
    }
}