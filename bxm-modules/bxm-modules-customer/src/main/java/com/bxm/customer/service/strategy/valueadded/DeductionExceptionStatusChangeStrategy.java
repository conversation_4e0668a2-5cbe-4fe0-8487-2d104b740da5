package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 扣款异常状态变更策略
 *
 * 处理从"扣款异常(待确认)"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. DEDUCTION_EXCEPTION -> DEDUCTION_CLOSED (关闭扣款)
 * 2. DEDUCTION_EXCEPTION -> CONFIRMED_PENDING_DEDUCTION (重新扣款)
 * 3. DEDUCTION_EXCEPTION -> DEDUCTION_COMPLETED (异常处理完成，直接完成扣款)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class DeductionExceptionStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED ||
               targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED) {
            validateCloseOperation(request, "扣款");
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION) {
            validateCustomerInfo(order);
            validateContactInfo(order);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED) {
            validateRemark(request.getRemark(), "异常解决");
        } else {
            throwUnsupportedTransition("扣款异常", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION;
    }
}
